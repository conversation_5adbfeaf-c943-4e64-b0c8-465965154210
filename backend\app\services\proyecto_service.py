from fastapi import HTTPException, status
from uuid import UUID, uuid4
import datetime
from typing import List, Dict, Any, Optional
from decimal import Decimal

from app.models.proyecto import (
    ProyectoCreate, ProyectoUpdate, Proyecto, ProyectoSummary,
    ProyectoListResponse, ProyectoEmpresaLink
)
from app.core.database import get_supabase_client

async def create_proyecto(proyecto_in: ProyectoCreate, user_id: UUID) -> Proyecto:
    """Create a new proyecto."""
    supabase = await get_supabase_client()

    # Generate UUID for the new proyecto
    proyecto_id = uuid4()

    # Prepare proyecto data
    proyecto_data = {
        "id": str(proyecto_id),
        "nombre": proyecto_in.nombre,
        "descripcion": proyecto_in.descripcion,
        "objetivo": proyecto_in.objetivo,
        "estado": proyecto_in.estado or "Planificado",
        "fecha_inicio": proyecto_in.fecha_inicio.isoformat() if proyecto_in.fecha_inicio else None,
        "fecha_fin_estimada": proyecto_in.fecha_fin_estimada.isoformat() if proyecto_in.fecha_fin_estimada else None,
        "fecha_fin_real": proyecto_in.fecha_fin_real.isoformat() if proyecto_in.fecha_fin_real else None,
        "presupuesto": float(proyecto_in.presupuesto) if proyecto_in.presupuesto else None,
        "prioridad": proyecto_in.prioridad or "Media",
        "progreso": float(proyecto_in.progreso) if proyecto_in.progreso else 0,
        "responsable_usuario_id": str(proyecto_in.responsable_usuario_id) if proyecto_in.responsable_usuario_id else None,
        "responsable_persona_id": str(proyecto_in.responsable_persona_id) if proyecto_in.responsable_persona_id else None,
        "info_adicional": proyecto_in.info_adicional,
        "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
        "updated_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
    }

    try:
        # Insert proyecto
        result = supabase.table('proyectos').insert(proyecto_data).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create proyecto"
            )

        # Create empresa associations if provided
        if proyecto_in.empresas_asociadas_ids:
            empresa_links = []
            for empresa_id in proyecto_in.empresas_asociadas_ids:
                empresa_links.append({
                    "proyecto_id": str(proyecto_id),
                    "empresa_id": str(empresa_id)
                })

            if empresa_links:
                supabase.table('proyectos_empresas').insert(empresa_links).execute()

        # Fetch and return the created proyecto
        return await get_proyecto_by_id(proyecto_id)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating proyecto: {str(e)}"
        )

async def get_proyecto_by_id(proyecto_id: UUID) -> Proyecto:
    """Get a proyecto by ID with related data."""
    supabase = await get_supabase_client()

    try:
        # Get proyecto with related data
        result = supabase.table('proyectos').select(
            """
            *,
            usuarios!proyectos_responsable_usuario_id_fkey(id, nombre, email),
            proyectos_empresas(empresas(id, nombre, tipo_relacion))
            """
        ).eq('id', str(proyecto_id)).maybe_single().execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Proyecto not found"
            )

        proyecto_data = result.data

        # Get task statistics
        tareas_result = supabase.table('tareas').select(
            "id, estado"
        ).eq('proyecto_id', str(proyecto_id)).execute()

        total_tareas = len(tareas_result.data) if tareas_result.data else 0
        tareas_completadas = len([t for t in (tareas_result.data or []) if t.get('estado') == 'Completada'])

        # Calculate progress percentage
        porcentaje_completado = (tareas_completadas / total_tareas * 100) if total_tareas > 0 else 0

        # Build response
        proyecto = Proyecto(
            id=UUID(proyecto_data['id']),
            nombre=proyecto_data['nombre'],
            descripcion=proyecto_data.get('descripcion'),
            objetivo=proyecto_data.get('objetivo'),
            estado=proyecto_data.get('estado', 'Planificado'),
            fecha_inicio=datetime.datetime.fromisoformat(proyecto_data['fecha_inicio']).date() if proyecto_data.get('fecha_inicio') else None,
            fecha_fin_estimada=datetime.datetime.fromisoformat(proyecto_data['fecha_fin_estimada']).date() if proyecto_data.get('fecha_fin_estimada') else None,
            fecha_fin_real=datetime.datetime.fromisoformat(proyecto_data['fecha_fin_real']).date() if proyecto_data.get('fecha_fin_real') else None,
            presupuesto=Decimal(str(proyecto_data['presupuesto'])) if proyecto_data.get('presupuesto') else None,
            prioridad=proyecto_data.get('prioridad', 'Media'),
            progreso=Decimal(str(proyecto_data.get('progreso', 0))),
            responsable_usuario_id=UUID(proyecto_data['responsable_usuario_id']) if proyecto_data.get('responsable_usuario_id') else None,
            responsable_persona_id=UUID(proyecto_data['responsable_persona_id']) if proyecto_data.get('responsable_persona_id') else None,
            info_adicional=proyecto_data.get('info_adicional'),
            created_at=datetime.datetime.fromisoformat(proyecto_data['created_at']),
            updated_at=datetime.datetime.fromisoformat(proyecto_data['updated_at']),
            total_tareas=total_tareas,
            tareas_completadas=tareas_completadas,
            porcentaje_completado=Decimal(str(porcentaje_completado))
        )

        return proyecto

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching proyecto: {str(e)}"
        )

async def get_proyectos(
    skip: int = 0,
    limit: int = 100,
    estado: Optional[str] = None,
    prioridad: Optional[str] = None,
    responsable_id: Optional[UUID] = None,
    search: Optional[str] = None
) -> ProyectoListResponse:
    """Get list of proyectos with filtering."""
    supabase = await get_supabase_client()

    try:
        # Build query
        query = supabase.table('proyectos').select(
            """
            *,
            usuarios!proyectos_responsable_usuario_id_fkey(id, nombre, email),
            proyectos_empresas(empresas(id, nombre))
            """,
            count='exact'
        )

        # Apply filters
        if estado:
            query = query.eq('estado', estado)
        if prioridad:
            query = query.eq('prioridad', prioridad)
        if responsable_id:
            query = query.eq('responsable_usuario_id', str(responsable_id))
        if search:
            query = query.or_(f'nombre.ilike.%{search}%,descripcion.ilike.%{search}%')

        # Apply pagination
        query = query.range(skip, skip + limit - 1).order('created_at', desc=True)

        result = query.execute()

        proyectos = []
        for proyecto_data in result.data or []:
            # Get task statistics for each proyecto
            tareas_result = supabase.table('tareas').select(
                "id, estado"
            ).eq('proyecto_id', proyecto_data['id']).execute()

            total_tareas = len(tareas_result.data) if tareas_result.data else 0
            tareas_completadas = len([t for t in (tareas_result.data or []) if t.get('estado') == 'Completada'])
            porcentaje_completado = (tareas_completadas / total_tareas * 100) if total_tareas > 0 else 0

            proyecto = Proyecto(
                id=UUID(proyecto_data['id']),
                nombre=proyecto_data['nombre'],
                descripcion=proyecto_data.get('descripcion'),
                objetivo=proyecto_data.get('objetivo'),
                estado=proyecto_data.get('estado', 'Planificado'),
                fecha_inicio=datetime.datetime.fromisoformat(proyecto_data['fecha_inicio']).date() if proyecto_data.get('fecha_inicio') else None,
                fecha_fin_estimada=datetime.datetime.fromisoformat(proyecto_data['fecha_fin_estimada']).date() if proyecto_data.get('fecha_fin_estimada') else None,
                fecha_fin_real=datetime.datetime.fromisoformat(proyecto_data['fecha_fin_real']).date() if proyecto_data.get('fecha_fin_real') else None,
                presupuesto=Decimal(str(proyecto_data['presupuesto'])) if proyecto_data.get('presupuesto') else None,
                prioridad=proyecto_data.get('prioridad', 'Media'),
                progreso=Decimal(str(proyecto_data.get('progreso', 0))),
                responsable_usuario_id=UUID(proyecto_data['responsable_usuario_id']) if proyecto_data.get('responsable_usuario_id') else None,
                responsable_persona_id=UUID(proyecto_data['responsable_persona_id']) if proyecto_data.get('responsable_persona_id') else None,
                info_adicional=proyecto_data.get('info_adicional'),
                created_at=datetime.datetime.fromisoformat(proyecto_data['created_at']),
                updated_at=datetime.datetime.fromisoformat(proyecto_data['updated_at']),
                total_tareas=total_tareas,
                tareas_completadas=tareas_completadas,
                porcentaje_completado=Decimal(str(porcentaje_completado))
            )
            proyectos.append(proyecto)

        return ProyectoListResponse(
            proyectos=proyectos,
            total=result.count or 0,
            page=skip // limit + 1,
            size=limit
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching proyectos: {str(e)}"
        )

async def update_proyecto(proyecto_id: UUID, proyecto_update: ProyectoUpdate) -> Proyecto:
    """Update a proyecto."""
    supabase = await get_supabase_client()

    try:
        # Prepare update data
        update_data = {}
        if proyecto_update.nombre is not None:
            update_data['nombre'] = proyecto_update.nombre
        if proyecto_update.descripcion is not None:
            update_data['descripcion'] = proyecto_update.descripcion
        if proyecto_update.objetivo is not None:
            update_data['objetivo'] = proyecto_update.objetivo
        if proyecto_update.estado is not None:
            update_data['estado'] = proyecto_update.estado
        if proyecto_update.fecha_inicio is not None:
            update_data['fecha_inicio'] = proyecto_update.fecha_inicio.isoformat()
        if proyecto_update.fecha_fin_estimada is not None:
            update_data['fecha_fin_estimada'] = proyecto_update.fecha_fin_estimada.isoformat()
        if proyecto_update.fecha_fin_real is not None:
            update_data['fecha_fin_real'] = proyecto_update.fecha_fin_real.isoformat()
        if proyecto_update.presupuesto is not None:
            update_data['presupuesto'] = float(proyecto_update.presupuesto)
        if proyecto_update.prioridad is not None:
            update_data['prioridad'] = proyecto_update.prioridad
        if proyecto_update.progreso is not None:
            update_data['progreso'] = float(proyecto_update.progreso)
        if proyecto_update.responsable_usuario_id is not None:
            update_data['responsable_usuario_id'] = str(proyecto_update.responsable_usuario_id)
        if proyecto_update.responsable_persona_id is not None:
            update_data['responsable_persona_id'] = str(proyecto_update.responsable_persona_id)
        if proyecto_update.info_adicional is not None:
            update_data['info_adicional'] = proyecto_update.info_adicional

        update_data['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()

        # Update proyecto
        result = supabase.table('proyectos').update(update_data).eq('id', str(proyecto_id)).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Proyecto not found"
            )

        return await get_proyecto_by_id(proyecto_id)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating proyecto: {str(e)}"
        )

async def delete_proyecto(proyecto_id: UUID) -> Dict[str, str]:
    """Delete a proyecto."""
    supabase = await get_supabase_client()

    try:
        # Check if proyecto exists
        existing = supabase.table('proyectos').select('id').eq('id', str(proyecto_id)).maybe_single().execute()

        if not existing.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Proyecto not found"
            )

        # Delete proyecto (cascade will handle related records)
        supabase.table('proyectos').delete().eq('id', str(proyecto_id)).execute()

        return {"message": "Proyecto deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting proyecto: {str(e)}"
        )

async def get_proyectos_activos_dashboard() -> List[ProyectoSummary]:
    """Get active proyectos for dashboard."""
    supabase = await get_supabase_client()

    try:
        # Get active proyectos (not completed or cancelled)
        result = supabase.table('proyectos').select(
            """
            id, nombre, estado, progreso, fecha_fin_estimada, responsable_usuario_id,
            usuarios!proyectos_responsable_usuario_id_fkey(nombre),
            proyectos_empresas(empresas(nombre))
            """
        ).not_.in_('estado', ['Completado', 'Cancelado']).order('prioridad').limit(6).execute()

        proyectos_summary = []
        for proyecto_data in result.data or []:
            # Get task count
            tareas_result = supabase.table('tareas').select(
                "id, estado"
            ).eq('proyecto_id', proyecto_data['id']).execute()

            total_tareas = len(tareas_result.data) if tareas_result.data else 0
            tareas_completadas = len([t for t in (tareas_result.data or []) if t.get('estado') == 'Completada'])

            # Get main empresa name
            empresa_principal = None
            if proyecto_data.get('proyectos_empresas') and len(proyecto_data['proyectos_empresas']) > 0:
                empresa_principal = proyecto_data['proyectos_empresas'][0]['empresas']['nombre']

            proyecto_summary = ProyectoSummary(
                id=UUID(proyecto_data['id']),
                nombre=proyecto_data['nombre'],
                estado=proyecto_data.get('estado', 'Planificado'),
                progreso=Decimal(str(proyecto_data.get('progreso', 0))),
                fecha_fin_estimada=datetime.datetime.fromisoformat(proyecto_data['fecha_fin_estimada']).date() if proyecto_data.get('fecha_fin_estimada') else None,
                responsable_usuario_id=UUID(proyecto_data['responsable_usuario_id']) if proyecto_data.get('responsable_usuario_id') else None,
                responsable_nombre=proyecto_data.get('usuarios', {}).get('nombre') if proyecto_data.get('usuarios') else None,
                empresa_principal=empresa_principal,
                total_tareas=total_tareas,
                tareas_completadas=tareas_completadas
            )
            proyectos_summary.append(proyecto_summary)

        return proyectos_summary

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard proyectos: {str(e)}"
        )
