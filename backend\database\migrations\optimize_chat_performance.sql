-- Chat Performance Optimization Migration
-- This migration adds indexes and optimizations for the chat/threads table

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_threads_thread_id_created_at ON threads(thread_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_threads_user_id_thread_id ON threads(user_id, thread_id);
CREATE INDEX IF NOT EXISTS idx_threads_type ON threads(type);
CREATE INDEX IF NOT EXISTS idx_threads_message_id ON threads(message_id);
CREATE INDEX IF NOT EXISTS idx_threads_created_at ON threads(created_at DESC);

-- Composite index for pagination queries
CREATE INDEX IF NOT EXISTS idx_threads_pagination ON threads(thread_id, created_at DESC, id);

-- Index for user authorization checks
CREATE INDEX IF NOT EXISTS idx_threads_auth_check ON threads(thread_id, user_id);

-- Add comments for documentation
COMMENT ON INDEX idx_threads_thread_id_created_at IS 'Optimizes message fetching with pagination for specific threads';
COMMENT ON INDEX idx_threads_user_id_thread_id IS 'Optimizes user authorization checks and thread listing';
COMMENT ON INDEX idx_threads_type IS 'Optimizes filtering by message type (user, agent, answer, etc.)';
COMMENT ON INDEX idx_threads_message_id IS 'Optimizes intermediate step lookups';
COMMENT ON INDEX idx_threads_pagination IS 'Optimizes paginated queries with consistent ordering';
COMMENT ON INDEX idx_threads_auth_check IS 'Optimizes thread authorization verification';

-- Optional: Add partial indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_threads_agent_messages ON threads(thread_id, created_at DESC) 
WHERE type IN ('answer', 'tool_use', 'tool_output', 'observation');

CREATE INDEX IF NOT EXISTS idx_threads_user_messages ON threads(thread_id, created_at DESC) 
WHERE type = 'user';

-- Add table statistics update for better query planning
ANALYZE threads;

-- Optional: Create a view for message counts per thread (can be materialized for better performance)
CREATE OR REPLACE VIEW thread_message_counts AS
SELECT 
    thread_id,
    COUNT(*) as message_count,
    MAX(created_at) as last_message_at,
    MIN(created_at) as first_message_at
FROM threads 
GROUP BY thread_id;

COMMENT ON VIEW thread_message_counts IS 'Provides quick access to message statistics per thread';

-- Function to get thread statistics efficiently
CREATE OR REPLACE FUNCTION get_thread_stats(p_thread_id bigint)
RETURNS TABLE(
    message_count bigint,
    last_message_at timestamp with time zone,
    first_message_at timestamp with time zone,
    user_message_count bigint,
    agent_message_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as message_count,
        MAX(t.created_at) as last_message_at,
        MIN(t.created_at) as first_message_at,
        COUNT(*) FILTER (WHERE t.type = 'user') as user_message_count,
        COUNT(*) FILTER (WHERE t.type IN ('answer', 'tool_use', 'tool_output', 'observation')) as agent_message_count
    FROM threads t
    WHERE t.thread_id = p_thread_id;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION get_thread_stats IS 'Efficiently retrieves comprehensive statistics for a specific thread';

-- Create a function for efficient message cleanup (for memory management)
CREATE OR REPLACE FUNCTION cleanup_old_messages(
    p_thread_id bigint,
    p_keep_recent_count integer DEFAULT 1000
) RETURNS integer AS $$
DECLARE
    deleted_count integer;
BEGIN
    -- Delete old messages, keeping only the most recent ones
    WITH messages_to_keep AS (
        SELECT id
        FROM threads
        WHERE thread_id = p_thread_id
        ORDER BY created_at DESC
        LIMIT p_keep_recent_count
    )
    DELETE FROM threads
    WHERE thread_id = p_thread_id
    AND id NOT IN (SELECT id FROM messages_to_keep);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_old_messages IS 'Removes old messages from a thread while keeping the most recent ones';

-- Create a trigger to automatically update thread statistics
CREATE OR REPLACE FUNCTION update_thread_stats_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- This could be used to maintain a separate thread_stats table
    -- For now, we'll just ensure the view is refreshed
    -- In a production environment, you might want to use a materialized view
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Optional: Create trigger for automatic stats updates
-- CREATE TRIGGER thread_stats_update_trigger
--     AFTER INSERT OR UPDATE OR DELETE ON threads
--     FOR EACH ROW EXECUTE FUNCTION update_thread_stats_trigger();

-- Performance monitoring query for administrators
CREATE OR REPLACE VIEW chat_performance_metrics AS
SELECT 
    thread_id,
    COUNT(*) as total_messages,
    COUNT(*) FILTER (WHERE type = 'user') as user_messages,
    COUNT(*) FILTER (WHERE type = 'answer') as agent_answers,
    COUNT(*) FILTER (WHERE type IN ('tool_use', 'tool_output', 'observation')) as intermediate_steps,
    MAX(created_at) as last_activity,
    MIN(created_at) as first_activity,
    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at))) / 3600 as conversation_duration_hours,
    AVG(LENGTH(content)) as avg_message_length,
    SUM(LENGTH(content)) as total_content_length
FROM threads 
WHERE content IS NOT NULL
GROUP BY thread_id
HAVING COUNT(*) > 10  -- Only show threads with significant activity
ORDER BY total_messages DESC;

COMMENT ON VIEW chat_performance_metrics IS 'Provides performance metrics for chat threads to identify potential issues';

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT ON thread_message_counts TO your_app_user;
-- GRANT SELECT ON chat_performance_metrics TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_thread_stats TO your_app_user;
-- GRANT EXECUTE ON FUNCTION cleanup_old_messages TO your_admin_user;
