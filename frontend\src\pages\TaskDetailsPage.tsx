import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Building2, 
  <PERSON>olderOpen, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Edit,
  Plus,
  Tag
} from 'lucide-react';
import { Tarea } from '../types/tarea';
import { 
  ESTADO_TAREA_COLORS, 
  PRIORIDAD_TAREA_COLORS, 
  URGENCIA_COLORS 
} from '../types/tarea';
import { apiClient } from '../lib/api';

const TaskDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [task, setTask] = useState<Tarea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchTask(id);
    }
  }, [id]);

  const fetchTask = async (taskId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.tareas.getById(taskId);
      setTask(response as Tarea);
    } catch (err: any) {
      console.error('Error fetching task:', err);
      setError(err.message || 'Error al cargar la tarea');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/tareas');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const getDaysText = (days?: number) => {
    if (days === undefined || days === null) return null;
    if (days < 0) return `${Math.abs(days)}d vencida`;
    if (days === 0) return 'Vence hoy';
    if (days === 1) return 'Vence mañana';
    return `${days}d restantes`;
  };

  const getProjectType = (task: Tarea) => {
    if (!task.proyecto_nombre) return null;
    
    const hasEmpresas = task.empresas_asociadas && task.empresas_asociadas.length > 0;
    return hasEmpresas ? 'Externo' : 'Interno (Aceleralia)';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando tarea...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar la tarea</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Volver a Tareas
          </button>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Tarea no encontrada</h2>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Volver a Tareas
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBack}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Volver a Tareas
          </button>
          
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{task.titulo}</h1>
              <div className="flex items-center space-x-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${ESTADO_TAREA_COLORS[task.estado]}`}>
                  {task.estado}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${PRIORIDAD_TAREA_COLORS[task.prioridad]}`}>
                  {task.prioridad}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${URGENCIA_COLORS[task.urgencia]}`}>
                  {task.urgencia}
                </span>
                {task.es_vencida && (
                  <span className="flex items-center text-red-600 text-sm font-medium">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Vencida
                  </span>
                )}
              </div>
            </div>
            
            <button
              onClick={() => navigate(`/tareas/${task.id}/editar`)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Description */}
            {task.descripcion && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Descripción</h2>
                <p className="text-gray-700 whitespace-pre-wrap">{task.descripcion}</p>
              </div>
            )}

            {/* Additional Info */}
            {task.info_adicional && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Información Adicional</h2>
                <p className="text-gray-700 whitespace-pre-wrap">{task.info_adicional}</p>
              </div>
            )}

            {/* Subtasks */}
            {task.subtareas && task.subtareas.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Subtareas ({task.subtareas.length})
                  </h2>
                  <button
                    onClick={() => navigate(`/tareas/nueva?parent=${task.id}`)}
                    className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Agregar
                  </button>
                </div>
                
                <div className="space-y-3">
                  {task.subtareas.map((subtask) => (
                    <div
                      key={subtask.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          subtask.estado === 'Completada' ? 'bg-green-500' : 'bg-gray-400'
                        }`} />
                        <span className={`font-medium ${
                          subtask.estado === 'Completada' ? 'text-gray-500 line-through' : 'text-gray-900'
                        }`}>
                          {subtask.titulo}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${ESTADO_TAREA_COLORS[subtask.estado]}`}>
                          {subtask.estado}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${PRIORIDAD_TAREA_COLORS[subtask.prioridad]}`}>
                          {subtask.prioridad}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Task Info */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Información de la Tarea</h3>
              
              <div className="space-y-4">
                {/* Due Date */}
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Fecha de vencimiento</p>
                    <p className="font-medium text-gray-900">
                      {formatDate(task.fecha_vencimiento)}
                      {task.dias_vencimiento !== null && (
                        <span className={`ml-2 text-sm ${task.es_vencida ? 'text-red-600' : 'text-gray-500'}`}>
                          ({getDaysText(task.dias_vencimiento)})
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                {/* Assigned User */}
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Asignado a</p>
                    <p className="font-medium text-gray-900">
                      {task.asignado_usuario?.nombre || 'Sin asignar'}
                    </p>
                  </div>
                </div>

                {/* Created by */}
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Creado por</p>
                    <p className="font-medium text-gray-900">
                      {task.creador_usuario?.nombre || 'Desconocido'}
                    </p>
                  </div>
                </div>

                {/* Parent Task */}
                {task.tarea_padre && (
                  <div className="flex items-center">
                    <FolderOpen className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Tarea padre</p>
                      <p className="font-medium text-gray-900">{task.tarea_padre}</p>
                    </div>
                  </div>
                )}

                {/* Completion Date */}
                {task.fecha_completado && (
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Fecha de completado</p>
                      <p className="font-medium text-gray-900">{formatDate(task.fecha_completado)}</p>
                    </div>
                  </div>
                )}

                {/* Created/Updated */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center mb-2">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <p className="text-xs text-gray-500">
                      Creado: {formatDate(task.created_at)}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <p className="text-xs text-gray-500">
                      Actualizado: {formatDate(task.updated_at)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Project Info */}
            {task.proyecto_nombre && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Proyecto</h3>

                <div className="space-y-4">
                  <div className="flex items-center">
                    <FolderOpen className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Nombre del proyecto</p>
                      <p className="font-medium text-gray-900">{task.proyecto_nombre}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Building2 className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Tipo de proyecto</p>
                      <p className="font-medium text-gray-900">{getProjectType(task)}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Associated Companies */}
            {task.empresas_asociadas && task.empresas_asociadas.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Empresas Asociadas ({task.empresas_asociadas.length})
                </h3>

                <div className="space-y-3">
                  {task.empresas_asociadas.map((empresa, index) => (
                    <div key={empresa.id || index} className="flex items-center">
                      <Building2 className="h-4 w-4 text-gray-400 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">{empresa.nombre}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tags */}
            {task.etiquetas && task.etiquetas.length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Etiquetas ({task.etiquetas.length})
                </h3>

                <div className="flex flex-wrap gap-2">
                  {task.etiquetas.map((etiqueta, index) => (
                    <span
                      key={etiqueta.id || index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                      style={{
                        backgroundColor: etiqueta.color ? `${etiqueta.color}20` : '#f3f4f6',
                        color: etiqueta.color || '#374151'
                      }}
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {etiqueta.nombre}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskDetailsPage;
