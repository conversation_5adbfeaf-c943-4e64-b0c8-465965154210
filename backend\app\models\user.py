from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from uuid import UUID
from datetime import datetime

class UserBase(BaseModel):
    """Base model for user data, shared properties."""
    email: EmailStr
    nombre: str
    rol: str
    empresa_id: Optional[UUID] = None
    avatar_url: Optional[str] = None
    info_adicional: Optional[str] = None

class UserCreate(UserBase):
    """Model for creating a new user (potentially via admin or signup)."""
    # Add password field if handling signup directly, otherwise Supabase handles it.
    # password: str
    pass

class UserUpdate(BaseModel):
    """Model for updating user data."""
    email: Optional[EmailStr] = None
    nombre: Optional[str] = None
    rol: Optional[str] = None
    empresa_id: Optional[UUID] = None
    avatar_url: Optional[str] = None
    info_adicional: Optional[str] = None

class UserInDBBase(UserBase):
    """Base model for user data stored in the database."""
    id: UUID
    ultimo_acceso: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True # Pydantic V2 alias for orm_mode

class User(UserInDBBase):
    """Model representing a user as returned from the API."""
    pass

class UserInDB(UserInDBBase):
    """Model representing a user stored in the database (potentially with more fields)."""
    pass