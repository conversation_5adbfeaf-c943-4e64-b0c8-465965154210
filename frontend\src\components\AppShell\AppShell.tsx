import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Header from './Header';
import SidebarNav from './SidebarNav';
import { ToastProvider } from '../../providers/ToastProvider';

interface AppShellProps {
  children: React.ReactNode;
}

const AppShell: React.FC<AppShellProps> = ({ children }) => {
  const location = useLocation();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    return savedState ? JSON.parse(savedState) : false;
  });
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if we're in chat page
  const isInChatPage = location.pathname.includes('/chat');

  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Close mobile menu on larger screens if it's open
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && isMobileMenuOpen) { // md breakpoint
        setIsMobileMenuOpen(false);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileMenuOpen]);

  return (
    <ToastProvider>
      <div className="flex flex-col h-screen">
        <Header toggleMobileMenu={toggleMobileMenu} />
        <div className="flex flex-1 overflow-hidden">
          {/* Desktop Sidebar */}
          <div className="hidden md:flex">
            <SidebarNav
              isCollapsed={isSidebarCollapsed}
              toggleSidebar={toggleSidebar}
              isMobileMenuOpen={false} // Not used for desktop
              toggleMobileMenu={() => {}} // Not used for desktop
            />
          </div>
          {/* Mobile Sidebar - Hidden when in chat page */}
          {isMobileMenuOpen && !isInChatPage && (
            <div className="md:hidden fixed inset-0 z-30 flex">
              <SidebarNav
                isCollapsed={false} // Mobile sidebar is never collapsed in this sense
                toggleSidebar={() => {}} // Desktop toggle not used here
                isMobileMenuOpen={isMobileMenuOpen}
                toggleMobileMenu={toggleMobileMenu} // To allow closing from within
              />
              {/* Backdrop */}
              <div
                className="fixed inset-0 bg-black opacity-50 z-20"
                onClick={toggleMobileMenu}
              ></div>
            </div>
          )}
          <main className="flex-1 p-6 overflow-y-auto bg-gray-100">
            {children}
          </main>
        </div>
      </div>
    </ToastProvider>
  );
};

export default AppShell;