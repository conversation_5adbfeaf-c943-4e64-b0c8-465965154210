import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body
from typing import Any

from ...core.security import verify_api_key
from ...services import sql_service
from ...services.text_modification_service import text_modification_service
from ...models.tool import (
    SqlExecutionRequest,
    SqlExecutionSuccessResponse,
    TextModificationRequest,
    TextModificationResponse,
    ErrorDetail
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post(
    "/execute_sql",
    response_model=SqlExecutionSuccessResponse,
    summary="Execute SQL Command via API Key",
    description="Allows authorized services (like n8n) to execute SQL commands using an API key.",
    dependencies=[Depends(verify_api_key)], # Apply API key authentication
    responses={
        400: {"model": ErrorDetail, "description": "Invalid SQL (Forbidden Pattern)"},
        401: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        403: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        500: {"model": ErrorDetail, "description": "Database Execution Error or Unexpected Error"}
    }
)
async def execute_sql_endpoint(
    request_body: SqlExecutionRequest = Body(...),
):
    """
    Endpoint to execute SQL commands.
    Requires `X-API-Key` header for authentication.
    Validates SQL against forbidden patterns before execution.
    """
    logger.info(f"Received request to execute SQL.") # Log entry point
    try:
        # Log the SQL type for non-sensitive overview, avoid logging full SQL for security
        sql_command_type = request_body.sql.strip().split(maxsplit=1)[0].upper()
        logger.debug(f"Attempting to execute SQL command of type: {sql_command_type}")

        result_data, rows_affected = await sql_service.execute_sql_command(request_body.sql)

        # Construct the success response based on what the service returned
        response_payload = {}
        if result_data is not None:
            response_payload["result"] = result_data
        if rows_affected is not None:
             response_payload["rows_affected"] = rows_affected
        # Always include status for non-select or successful empty select
        if result_data is None or isinstance(result_data, list):
             response_payload["status"] = "success"

        logger.info(f"SQL execution successful. Type: {sql_command_type}, Rows affected: {rows_affected}")
        return SqlExecutionSuccessResponse(**response_payload)

    except HTTPException as http_exc:
        # Log the known HTTP exception before re-raising
        logger.warning(f"HTTPException during SQL execution: Status={http_exc.status_code}, Detail={http_exc.detail}")
        # Re-raise HTTPExceptions raised by the service (e.g., 400 validation, 500 DB error)
        # Ensure the detail is included in the response
        raise HTTPException(status_code=http_exc.status_code, detail=http_exc.detail)
    except Exception as e:
        # Catch and log any other unexpected errors during endpoint processing
        logger.error(f"Unexpected error in /execute_sql endpoint: {e}", exc_info=True) # Include traceback
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected server error occurred: {str(e)}"
        )

@router.post(
    "/modificar_texto_campo_bd",
    response_model=TextModificationResponse,
    summary="Modify Text Field Content via API Key",
    description="Allows authorized services (like n8n) to perform granular modifications on text fields using an API key.",
    dependencies=[Depends(verify_api_key)], # Apply API key authentication
    responses={
        400: {"model": ErrorDetail, "description": "Invalid request data or table/column not found"},
        401: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        403: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        404: {"model": ErrorDetail, "description": "Record not found"},
        500: {"model": ErrorDetail, "description": "Database error or unexpected error"}
    }
)
async def modify_text_field_endpoint(
    request_body: TextModificationRequest = Body(...),
):
    """
    Endpoint to modify text field content with granular instructions.
    Requires `X-API-Key` header for authentication.
    Applies a sequence of modification instructions to the specified text field.
    """
    logger.info(f"Received request to modify text field {request_body.tabla}.{request_body.columna}")
    try:
        # Log basic request info for debugging (avoid logging sensitive content)
        logger.debug(f"Processing {len(request_body.instrucciones_de_cambio)} instructions for record {request_body.id_fila}")

        result = await text_modification_service.process_text_modification(request_body)

        logger.info(f"Text modification completed. Status: {result.status}, Changes made: {result.cambios_realizados_en_bd}")
        return result

    except HTTPException as http_exc:
        # Log the known HTTP exception before re-raising
        logger.warning(f"HTTPException during text modification: Status={http_exc.status_code}, Detail={http_exc.detail}")
        # Re-raise HTTPExceptions raised by the service
        raise HTTPException(status_code=http_exc.status_code, detail=http_exc.detail)
    except Exception as e:
        # Catch and log any other unexpected errors during endpoint processing
        logger.error(f"Unexpected error in /modificar_texto_campo_bd endpoint: {e}", exc_info=True) # Include traceback
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected server error occurred: {str(e)}"
        )