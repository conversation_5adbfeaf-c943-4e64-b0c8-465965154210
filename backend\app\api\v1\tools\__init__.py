"""
Tools package for AI agent tools.

This package contains individual tool implementations that can be used by AI agents
through n8n workflows or other automation systems.

Available tools:
- SQL Execution Tool: Execute SQL commands with security validation
- Text Modification Tool: Perform granular text field modifications
"""

import logging
from fastapi import APIRouter

# Import all tool routers for easy access
from .sql_execution import router as sql_execution_router
from .text_modification import router as text_modification_router

logger = logging.getLogger(__name__)

# Create main router that includes all tool routers
router = APIRouter()

# Include individual tool routers
logger.info("Registering SQL execution tool...")
router.include_router(sql_execution_router, tags=["SQL Execution Tool"])

logger.info("Registering text modification tool...")
router.include_router(text_modification_router, tags=["Text Modification Tool"])

__all__ = [
    "router",
    "sql_execution_router",
    "text_modification_router"
]
