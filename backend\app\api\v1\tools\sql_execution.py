"""
SQL Execution Tool

This tool allows authorized services (like n8n) to execute SQL commands
using an API key for authentication. It includes security validation
to prevent dangerous operations.

Features:
- API key authentication
- SQL pattern validation (blocks dangerous operations)
- Comprehensive logging
- Structured error handling
- Support for SELECT, INSERT, UPDATE operations
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body
from typing import Any

from ....core.security import verify_api_key
from ....services import sql_service
from ....models.tool import (
    SqlExecutionRequest,
    SqlExecutionSuccessResponse,
    ErrorDetail
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post(
    "/execute_sql",
    response_model=SqlExecutionSuccessResponse,
    summary="Execute SQL Command via API Key",
    description="Allows authorized services (like n8n) to execute SQL commands using an API key.",
    dependencies=[Depends(verify_api_key)], # Apply API key authentication
    responses={
        400: {"model": ErrorDetail, "description": "Invalid SQL (Forbidden Pattern)"},
        401: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        403: {"model": ErrorDetail, "description": "Missing or Invalid API Key"}, # Handled by dependency
        500: {"model": ErrorDetail, "description": "Database Execution Error or Unexpected Error"}
    }
)
async def execute_sql_endpoint(
    request_body: SqlExecutionRequest = Body(...),
):
    """
    Endpoint to execute SQL commands.
    Requires `X-API-Key` header for authentication.
    Validates SQL against forbidden patterns before execution.
    
    Args:
        request_body: SQL execution request containing the SQL command
        
    Returns:
        SqlExecutionSuccessResponse: Result data, rows affected, and status
        
    Raises:
        HTTPException 400: Invalid SQL (contains forbidden patterns)
        HTTPException 401/403: Missing or invalid API key
        HTTPException 500: Database execution error or unexpected error
    """
    logger.info(f"Received request to execute SQL.") # Log entry point
    try:
        # Log the SQL type for non-sensitive overview, avoid logging full SQL for security
        sql_command_type = request_body.sql.strip().split(maxsplit=1)[0].upper()
        logger.debug(f"Attempting to execute SQL command of type: {sql_command_type}")

        result_data, rows_affected = await sql_service.execute_sql_command(request_body.sql)

        # Construct the success response based on what the service returned
        response_payload = {}
        if result_data is not None:
            response_payload["result"] = result_data
        if rows_affected is not None:
             response_payload["rows_affected"] = rows_affected
        # Always include status for non-select or successful empty select
        if result_data is None or isinstance(result_data, list):
             response_payload["status"] = "success"

        logger.info(f"SQL execution successful. Type: {sql_command_type}, Rows affected: {rows_affected}")
        return SqlExecutionSuccessResponse(**response_payload)

    except HTTPException as http_exc:
        # Log the known HTTP exception before re-raising
        logger.warning(f"HTTPException during SQL execution: Status={http_exc.status_code}, Detail={http_exc.detail}")
        # Re-raise HTTPExceptions raised by the service (e.g., 400 validation, 500 DB error)
        # Ensure the detail is included in the response
        raise HTTPException(status_code=http_exc.status_code, detail=http_exc.detail)
    except Exception as e:
        # Catch and log any other unexpected errors during endpoint processing
        logger.error(f"Unexpected error in /execute_sql endpoint: {e}", exc_info=True) # Include traceback
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected server error occurred: {str(e)}"
        )
