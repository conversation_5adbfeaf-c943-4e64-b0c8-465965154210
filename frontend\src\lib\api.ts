/**
 * API Client for Project Management System
 * Centralized HTTP client for all backend communication
 */

import { supabase } from './supabase';

// Get API URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

/**
 * HTTP Client with automatic error handling and authentication
 */
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Set authentication token for all requests
   */
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove authentication token
   */
  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Generic HTTP request method
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Get current session token from Supabase
    const { data: { session } } = await supabase.auth.getSession();
    const headers = { ...this.defaultHeaders };

    // Add authorization header if user is authenticated
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      // Handle non-JSON responses (like 204 No Content)
      if (response.status === 204) {
        return {} as T;
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API Error [${options.method || 'GET'}] ${url}:`, error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    let url = endpoint;
    
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    return this.request<T>(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create API client instance
const api = new ApiClient(API_BASE_URL);

/**
 * Organized API endpoints by feature
 */
export const apiClient = {
  // Dashboard endpoints
  dashboard: {
    getData: () => api.get('/api/v1/dashboard'),
    getQuickActions: () => api.get('/api/v1/dashboard/quick-actions'),
    getNotifications: () => api.get('/api/v1/dashboard/notifications'),
  },

  // Projects endpoints
  proyectos: {
    getAll: (params?: any) => api.get('/api/v1/proyectos', params),
    getById: (id: string) => api.get(`/api/v1/proyectos/${id}`),
    create: (data: any) => api.post('/api/v1/proyectos', data),
    update: (id: string, data: any) => api.put(`/api/v1/proyectos/${id}`, data),
    delete: (id: string) => api.delete(`/api/v1/proyectos/${id}`),
    getEstados: () => api.get('/api/v1/proyectos/estados/available'),
    getPrioridades: () => api.get('/api/v1/proyectos/prioridades/available'),
  },

  // Processes endpoints
  procesos: {
    getAll: (params?: any) => api.get('/api/v1/procesos', params),
    getById: (id: string) => api.get(`/api/v1/procesos/${id}`),
    create: (data: any) => api.post('/api/v1/procesos', data),
    update: (id: string, data: any) => api.put(`/api/v1/procesos/${id}`, data),
    delete: (id: string) => api.delete(`/api/v1/procesos/${id}`),
    getTipos: () => api.get('/api/v1/procesos/tipos/available'),
    // getEstados removed - procesos table doesn't have estado column
  },

  // Tasks endpoints
  tareas: {
    getAll: (params?: any) => api.get('/api/v1/tareas', params),
    getById: (id: string) => api.get(`/api/v1/tareas/${id}`),
    create: (data: any) => api.post('/api/v1/tareas', data),
    update: (id: string, data: any) => api.put(`/api/v1/tareas/${id}`, data),
    delete: (id: string) => api.delete(`/api/v1/tareas/${id}`),
    getMatrix: (params?: any) => api.get('/api/v1/tareas/matrix', params),
    getKanban: (params?: any) => api.get('/api/v1/tareas/kanban', params),
    getEstados: () => api.get('/api/v1/tareas/estados/available'),
    getPrioridades: () => api.get('/api/v1/tareas/prioridades/available'),
    getUrgencias: () => api.get('/api/v1/tareas/urgencias/available'),
    getProyectos: () => api.get('/api/v1/tareas/proyectos/available'),
    getEmpresas: () => api.get('/api/v1/tareas/empresas/available'),
  },

  // Search endpoints
  search: {
    global: (query: string, params?: any) => 
      api.get('/api/v1/search', { q: query, ...params }),
    quick: (query: string, limit?: number) => 
      api.get('/api/v1/search/quick', { q: query, limit }),
    suggestions: (query: string, limit?: number) => 
      api.get('/api/v1/search/suggestions', { q: query, limit }),
  },

  // Health check
  health: () => api.get('/health'),
};

// Export the raw API client for custom requests
export { api };

// Export auth methods for easy access
export const setAuthToken = (token: string) => api.setAuthToken(token);
export const clearAuthToken = () => api.clearAuthToken();

// Default export
export default apiClient;
