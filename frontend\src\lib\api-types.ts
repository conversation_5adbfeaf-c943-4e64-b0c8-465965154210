/**
 * API Types for Project Management System
 * Centralized type definitions for API requests and responses
 *
 * Note: These types are designed to be compatible with existing types in src/types/
 * They represent the raw API responses before transformation to UI types
 */

// Re-export existing types for compatibility
export type {
  Tarea,
  TareaCreate,
  TareaUpdate,
  TareaSummary,
  TareaMatrix,
  TareaKanbanBoard,
  TareaListResponse,
  EstadoTarea,
  PrioridadTarea,
  UrgenciaTarea
} from '../types/tarea';

export type {
  DashboardData,
  QuickAction,
  DashboardNotification as Notification,
  QuickActionsResponse,
  DashboardNotificationsResponse
} from '../types/dashboard';

export type {
  Proyecto,
  ProyectoCreate,
  ProyectoUpdate,
  ProyectoListResponse
} from '../types/proyecto';

export type {
  Proceso,
  ProcesoCreate,
  ProcesoUpdate,
  ProcesoListResponse
} from '../types/proceso';

export type {
  SearchResult,
  GlobalSearchResponse,
  SearchFiltersResponse
} from '../types/search';

// Base types for common API patterns
export type ApiResponse<T> = T;

export interface PaginationParams {
  skip?: number;
  limit?: number;
  page?: number;
}

export interface SearchParams extends PaginationParams {
  q?: string;
  query?: string;
}

export interface FilterParams {
  estado?: string;
  prioridad?: string;
  urgencia?: string;
  proyecto_id?: string;
  empresa_id?: string;
  asignado_a?: string;
  fecha_inicio?: string;
  fecha_fin?: string;
}

// Additional API-specific types that don't exist in the main types

// Available options types (for dropdowns and filters)
export interface AvailableOption {
  value: string;
  label: string;
  count?: number;
}

// Options for specific entities
export interface ProyectoOption {
  id: string;
  nombre: string;
}

export interface EmpresaOption {
  id: string;
  nombre: string;
}

// Generic API parameter types
export type ApiParams = Record<string, string | number | boolean | undefined | null>;

// Request body types for different HTTP methods
export type ApiRequestBody = Record<string, unknown> | FormData | string | null | undefined | object;

// Generic API error response
export interface ApiError {
  detail: string;
  status_code?: number;
  error_code?: string;
}

// Health check response
export interface HealthResponse {
  status: string;
  timestamp: string;
}
