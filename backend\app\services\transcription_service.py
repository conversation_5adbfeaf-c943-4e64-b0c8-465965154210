import httpx
import logging
from fastapi import HTTPException, UploadFile, status
from app.core.config import settings # Import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get OpenAI configuration from environment variables
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") # Removed - Use settings object instead
OPENAI_TRANSCRIPTION_URL = "https://api.openai.com/v1/audio/transcriptions" # Standard Whisper API endpoint

async def transcribe_audio(file: UploadFile) -> str:
    """
    Transcribes the given audio file using the OpenAI Whisper API.

    Args:
        file: The audio file uploaded via FastAPI.

    Returns:
        The transcribed text.

    Raises:
        HTTPException: If the API key is missing, the request fails,
                       or the API returns an error.
    """
    # Use settings object to access the API key
    if not settings.OPENAI_API_KEY:
        logger.error("OPENAI_API_KEY environment variable not set.")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Transcription service is not configured.",
        )

    async with httpx.AsyncClient() as client:
        try:
            files = {"file": (file.filename, await file.read(), file.content_type)}
            headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"} # Use settings
            data = {"model": "gpt-4o-mini-transcribe"} # Use requested model

            logger.info(f"Sending transcription request for file: {file.filename}")
            response = await client.post(
                OPENAI_TRANSCRIPTION_URL,
                headers=headers,
                files=files,
                data=data,
                timeout=60.0 # Add a timeout
            )
            response.raise_for_status() # Raise exception for 4xx/5xx responses

            response_data = response.json()
            transcribed_text = response_data.get("text")

            if transcribed_text is None:
                logger.error(f"Transcription failed. Unexpected response format: {response_data}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Transcription failed due to unexpected API response.",
                )

            logger.info(f"Transcription successful for file: {file.filename}")
            return transcribed_text

        except httpx.RequestError as exc:
            logger.error(f"HTTP request failed during transcription: {exc}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Could not connect to transcription service: {exc}",
            )
        except httpx.HTTPStatusError as exc:
            logger.error(f"Transcription API returned error status {exc.response.status_code}: {exc.response.text}")
            detail = f"Transcription failed: API returned status {exc.response.status_code}."
            try:
                # Try to parse more specific error from OpenAI response
                error_detail = exc.response.json().get("error", {}).get("message")
                if error_detail:
                    detail += f" Error: {error_detail}"
            except Exception:
                pass # Ignore if parsing fails
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=detail,
            )
        except Exception as exc:
            logger.exception(f"An unexpected error occurred during transcription: {exc}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred during transcription: {exc}",
            )