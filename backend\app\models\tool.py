from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List, Literal
from uuid import UUID
from datetime import datetime

class ToolBase(BaseModel):
    """Base model for tool data."""
    tool_name: str
    tool_description: Optional[str] = None
    tool_params: Dict[str, Any] = Field(default_factory=dict) # For jsonb
    function_identifier: Optional[str] = None # Identifier for backend function/logic

class ToolCreate(ToolBase):
    """Model for creating a new tool."""
    pass

class ToolUpdate(BaseModel):
    """Model for updating tool data."""
    tool_name: Optional[str] = None
    tool_description: Optional[str] = None
    tool_params: Optional[Dict[str, Any]] = None
    function_identifier: Optional[str] = None

class ToolInDBBase(ToolBase):
    """Base model for tool data stored in the database."""
    id: UUID
    creado_en: Optional[datetime] = None # Allow null based on schema? Default is now()
    updated_at: Optional[datetime] = None # Allow null based on schema? Default is now()

    class Config:
        from_attributes = True # Pydantic V2 alias for orm_mode

class Tool(ToolInDBBase):
    """Model representing a tool as returned from the API."""
    pass

class ToolInDB(ToolInDBBase):
    """Model representing a tool stored in the database."""
    pass
# --- Models for SQL Execution Tool Endpoint ---

class SqlExecutionRequest(BaseModel):
    """Request body for the SQL execution endpoint."""
    sql: str = Field(..., description="The SQL command to execute.")

class SqlExecutionSuccessResponse(BaseModel):
    """Response body for successful SQL execution."""
    status: Optional[str] = Field(None, description="Status message, e.g., 'success' for non-SELECT queries.")
    result: Optional[list[dict[str, Any]]] = Field(None, description="Query results for SELECT statements (list of dicts).")
    rows_affected: Optional[int] = Field(None, description="Number of rows affected by INSERT/UPDATE/DELETE.")

class ErrorDetail(BaseModel):
    """Generic error detail model for API responses."""
    detail: str

# --- Models for Text Field Modification Tool Endpoint ---

class InstruccionCambio(BaseModel):
    """Base model for text modification instructions."""
    accion: Literal["reemplazar_fragmento", "anadir_texto", "eliminar_fragmento", "eliminar_seccion_delimitada"]

    # Fields for reemplazar_fragmento
    texto_a_buscar: Optional[str] = None
    nuevo_texto: Optional[str] = None
    reemplazar_todas_las_ocurrencias: Optional[bool] = False

    # Fields for anadir_texto
    posicion: Optional[Literal["inicio_campo", "final_campo", "despues_de_marcador", "antes_de_marcador"]] = None
    texto_a_anadir: Optional[str] = None
    texto_marcador_referencia: Optional[str] = None
    crear_marcador_si_no_existe: Optional[bool] = False

    # Fields for eliminar_fragmento
    texto_a_eliminar: Optional[str] = None
    eliminar_todas_las_ocurrencias: Optional[bool] = False

    # Fields for eliminar_seccion_delimitada
    marcador_inicio_seccion: Optional[str] = None
    marcador_fin_seccion: Optional[str] = None
    eliminar_solo_primera_seccion: Optional[bool] = True

class TextModificationRequest(BaseModel):
    """Request body for the text field modification endpoint."""
    tabla: str = Field(..., description="Nombre de la tabla en la base de datos")
    columna: str = Field(..., description="Nombre de la columna de texto a modificar")
    id_fila: str = Field(..., description="ID (UUID) del registro a modificar")
    instrucciones_de_cambio: List[InstruccionCambio] = Field(..., description="Lista de instrucciones de cambio a aplicar")

class TextModificationResponse(BaseModel):
    """Response body for text field modification operations."""
    status: Literal["success", "partial_success", "no_change_applied", "error"]
    message: str = Field(..., description="Mensaje descriptivo del resultado")
    cambios_realizados_en_bd: bool = Field(..., description="Indica si se realizaron cambios en la base de datos")
    instrucciones_procesadas: Optional[int] = Field(None, description="Número total de instrucciones procesadas")
    instrucciones_exitosas: Optional[int] = Field(None, description="Número de instrucciones aplicadas exitosamente")