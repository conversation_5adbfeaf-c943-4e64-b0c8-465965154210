from pydantic import BaseModel, EmailStr, HttpUrl
from typing import Optional, Literal
from uuid import UUID
import datetime # Required for date

class EmpresaBase(BaseModel):
    nombre: str
    nif_cif: Optional[str] = None
    sector: Optional[str] = None
    descripcion: Optional[str] = None
    logo_url: Optional[HttpUrl] = None
    direccion: Optional[str] = None
    direccion_fiscal: Optional[str] = None
    telefono: Optional[str] = None
    email_principal: Optional[EmailStr] = None
    website: Optional[HttpUrl] = None
    tipo_empresa: Optional[str] = None # Consider ENUM if predefined list
    tipo_relacion: Optional[Literal['Cliente', 'Colaborador', 'Otro']] = None
    activo: bool = True
    info_adicional: Optional[str] = None

class EmpresaCreate(EmpresaBase):
    pass

class Empresa(EmpresaBase):
    id: UUID
    fecha_alta: Optional[datetime.date] = None # From DB schema, not in form doc initially
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True
