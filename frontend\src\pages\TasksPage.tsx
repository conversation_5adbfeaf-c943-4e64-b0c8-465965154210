import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTasks, useTaskKanban, useProyectos, useEmpresas } from '../hooks/useTasks';
import { useTasksRealtime } from '../hooks/useRealtime';
import { 
  TareaFilters, 
  ESTADOS_TAREA, 
  PRIORIDADES_TAREA, 
  URGENCIAS_TAREA,
  ESTADO_TAREA_COLORS,
  PRIORIDAD_TAREA_COLORS,
  URGENCIA_COLORS
} from '../types/tarea';
import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Calendar,
  User,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Clock
} from 'lucide-react';

const TasksPage: React.FC = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'list' | 'kanban'>('kanban');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<TareaFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  const {
    tasks,
    loading: listLoading,
    error,
    total,
    currentPage,
    pageSize,
    fetchTasks,
    setError
  } = useTasks();

  // Memoize filters to prevent infinite re-renders
  const memoizedFilters = useMemo(() => ({
    ...filters,
    search: searchTerm
  }), [filters, searchTerm]);

  const {
    kanbanBoard,
    loading: kanbanLoading,
    fetchKanban
  } = useTaskKanban(memoizedFilters);

  const { proyectos } = useProyectos();
  const { empresas } = useEmpresas();

  const loading = viewMode === 'list' ? listLoading : kanbanLoading;

  // Setup realtime updates
  useTasksRealtime(
    () => {
      if (viewMode === 'list') {
        fetchTasks(currentPage, { ...filters, search: searchTerm });
      } else {
        fetchKanban();
      }
    },
    () => {
      if (viewMode === 'list') {
        fetchTasks(currentPage, { ...filters, search: searchTerm });
      } else {
        fetchKanban();
      }
    }
  );

  // Apply filters when they change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (viewMode === 'list') {
        fetchTasks(1, { ...filters, search: searchTerm });
      } else {
        fetchKanban();
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, viewMode]);

  const handleCreateTask = () => {
    navigate('/tareas/nueva');
  };

  const handleTaskClick = (taskId: string) => {
    navigate(`/tareas/${taskId}`);
  };

  const handlePageChange = (page: number) => {
    fetchTasks(page, { ...filters, search: searchTerm });
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const getDaysText = (days?: number) => {
    if (days === undefined || days === null) return null;
    if (days < 0) return `${Math.abs(days)}d vencida`;
    if (days === 0) return 'Vence hoy';
    if (days === 1) return 'Vence mañana';
    return `${days}d restantes`;
  };

  // const handleStatusChange = async (taskId: string, newStatus: string) => {
  //   try {
  //     await updateTask(taskId, { estado: newStatus as any });
  //     // Refresh the appropriate view
  //     if (viewMode === 'list') {
  //       fetchTasks(currentPage, { ...filters, search: searchTerm });
  //     } else {
  //       fetchKanban();
  //     }
  //   } catch (error) {
  //     console.error('Error updating task status:', error);
  //   }
  // };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar tareas
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              if (viewMode === 'list') {
                fetchTasks(1, { ...filters, search: searchTerm });
              } else {
                fetchKanban();
              }
            }}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tareas</h1>
            <p className="text-gray-600 mt-1">
              Organiza y gestiona todas tus tareas
            </p>
          </div>
          
          <button
            onClick={handleCreateTask}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nueva Tarea
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar tareas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* View Mode and Filters */}
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('kanban')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'kanban' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg transition-colors ${
                  showFilters || Object.keys(filters).length > 0
                    ? 'border-blue-300 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {Object.keys(filters).length > 0 && (
                  <span className="ml-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {Object.keys(filters).length}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estado
                  </label>
                  <select
                    value={filters.estado || ''}
                    onChange={(e) => setFilters({ ...filters, estado: e.target.value as any || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los estados</option>
                    {ESTADOS_TAREA.map((estado) => (
                      <option key={estado} value={estado}>
                        {estado}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Prioridad
                  </label>
                  <select
                    value={filters.prioridad || ''}
                    onChange={(e) => setFilters({ ...filters, prioridad: e.target.value as any || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las prioridades</option>
                    {PRIORIDADES_TAREA.map((prioridad) => (
                      <option key={prioridad} value={prioridad}>
                        {prioridad}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Urgencia
                  </label>
                  <select
                    value={filters.urgencia || ''}
                    onChange={(e) => setFilters({ ...filters, urgencia: e.target.value as any || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las urgencias</option>
                    {URGENCIAS_TAREA.map((urgencia) => (
                      <option key={urgencia} value={urgencia}>
                        {urgencia}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Proyecto
                  </label>
                  <select
                    value={filters.proyecto_id || ''}
                    onChange={(e) => setFilters({ ...filters, proyecto_id: e.target.value || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los proyectos</option>
                    {proyectos.map((proyecto) => (
                      <option key={proyecto.id} value={proyecto.id}>
                        {proyecto.nombre}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Empresa
                  </label>
                  <select
                    value={filters.empresa_id || ''}
                    onChange={(e) => setFilters({ ...filters, empresa_id: e.target.value || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las empresas</option>
                    {empresas.map((empresa) => (
                      <option key={empresa.id} value={empresa.id}>
                        {empresa.nombre}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={clearFilters}
                    className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Limpiar filtros
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-sm text-gray-600">
            {viewMode === 'list' 
              ? `Mostrando ${tasks.length} de ${total} tareas`
              : `Total: ${kanbanBoard?.total_tareas || 0} tareas`
            }
          </p>
          
          {loading && (
            <div className="flex items-center text-sm text-gray-600">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Cargando...
            </div>
          )}
        </div>

        {/* Tasks List/Kanban */}
        {viewMode === 'list' ? (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {tasks.length === 0 ? (
              <div className="text-center py-12">
                <CheckCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron tareas
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || Object.keys(filters).length > 0
                    ? 'Intenta ajustar los filtros de búsqueda.'
                    : 'Crea tu primera tarea para comenzar.'}
                </p>
                {!searchTerm && Object.keys(filters).length === 0 && (
                  <button
                    onClick={handleCreateTask}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Tarea
                  </button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tarea
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Estado
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Prioridad
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Asignado
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vencimiento
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Proyecto
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tasks.map((task) => (
                      <tr
                        key={task.id}
                        onClick={() => handleTaskClick(task.id)}
                        className="hover:bg-gray-50 cursor-pointer transition-colors"
                      >
                        <td className="px-6 py-4">
                          <div className="flex items-start">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">
                                {task.titulo}
                              </div>
                              {task.descripcion && (
                                <div className="text-sm text-gray-500 line-clamp-1">
                                  {task.descripcion}
                                </div>
                              )}
                            </div>
                            {task.es_vencida && (
                              <AlertTriangle className="h-4 w-4 text-red-500 ml-2 flex-shrink-0" />
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ESTADO_TAREA_COLORS[task.estado]}`}>
                            {task.estado}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${PRIORIDAD_TAREA_COLORS[task.prioridad]}`}>
                              {task.prioridad}
                            </span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${URGENCIA_COLORS[task.urgencia]}`}>
                              {task.urgencia}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {task.asignado_usuario ? (
                            <div className="flex items-center">
                              <User className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">
                                {task.asignado_usuario.nombre}
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Sin asignar</span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          {task.fecha_vencimiento ? (
                            <div className={`flex items-center text-sm ${
                              task.es_vencida ? 'text-red-600' : 'text-gray-900'
                            }`}>
                              <Calendar className="h-4 w-4 mr-2" />
                              <div>
                                <div>{formatDate(task.fecha_vencimiento)}</div>
                                {task.dias_vencimiento !== undefined && (
                                  <div className="text-xs">
                                    {getDaysText(task.dias_vencimiento)}
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Sin fecha</span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          {task.proyecto_nombre ? (
                            <span className="text-sm text-gray-900">
                              {task.proyecto_nombre}
                            </span>
                          ) : (
                            <span className="text-sm text-gray-500">Sin proyecto</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ) : (
          // Kanban View
          <div className="overflow-x-auto">
            {kanbanBoard ? (
              <div className="flex space-x-6 pb-6" style={{ minWidth: '1200px' }}>
                {kanbanBoard.columnas.map((columna) => (
                  <div key={columna.estado} className="flex-shrink-0 w-80">
                    <div className="bg-white rounded-lg border border-gray-200">
                      <div className={`px-4 py-3 border-b border-gray-200 ${ESTADO_TAREA_COLORS[columna.estado]}`}>
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-sm">{columna.estado}</h3>
                          <span className="text-xs font-medium">{columna.total}</span>
                        </div>
                      </div>
                      
                      <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
                        {columna.tareas.length === 0 ? (
                          <div className="text-center py-8 text-gray-500">
                            <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p className="text-sm">No hay tareas</p>
                          </div>
                        ) : (
                          columna.tareas.map((task) => (
                            <div
                              key={task.id}
                              onClick={() => handleTaskClick(task.id)}
                              className={`
                                p-3 bg-gray-50 rounded-lg border cursor-pointer hover:shadow-md transition-all
                                ${task.es_vencida ? 'border-red-200 bg-red-50' : 'border-gray-200 hover:border-gray-300'}
                              `}
                            >
                              <div className="flex items-start justify-between mb-2">
                                <h4 className="text-sm font-medium text-gray-900 line-clamp-2 flex-1">
                                  {task.titulo}
                                </h4>
                                {task.es_vencida && (
                                  <AlertTriangle className="h-4 w-4 text-red-500 ml-2 flex-shrink-0" />
                                )}
                              </div>
                              
                              <div className="space-y-2">
                                {task.proyecto_nombre && (
                                  <div className="text-xs text-gray-600 truncate">
                                    📁 {task.proyecto_nombre}
                                  </div>
                                )}
                                
                                {task.asignado_nombre && (
                                  <div className="flex items-center text-xs text-gray-600">
                                    <User className="h-3 w-3 mr-1 flex-shrink-0" />
                                    <span className="truncate">{task.asignado_nombre}</span>
                                  </div>
                                )}
                                
                                {task.fecha_vencimiento && (
                                  <div className={`flex items-center text-xs ${
                                    task.es_vencida ? 'text-red-600' : 'text-gray-600'
                                  }`}>
                                    <Calendar className="h-3 w-3 mr-1 flex-shrink-0" />
                                    <span>{formatDate(task.fecha_vencimiento)}</span>
                                    {task.dias_vencimiento !== undefined && (
                                      <span className="ml-1">({getDaysText(task.dias_vencimiento)})</span>
                                    )}
                                  </div>
                                )}
                              </div>
                              
                              <div className="flex items-center justify-between mt-3">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${PRIORIDAD_TAREA_COLORS[task.prioridad]}`}>
                                  {task.prioridad}
                                </span>
                                
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${URGENCIA_COLORS[task.urgencia]}`}>
                                  {task.urgencia}
                                </span>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
                <Grid className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Cargando vista Kanban...
                </h3>
              </div>
            )}
          </div>
        )}

        {/* Pagination for List View */}
        {viewMode === 'list' && total > pageSize && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Página {currentPage} de {Math.ceil(total / pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Anterior
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Siguiente
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TasksPage;
