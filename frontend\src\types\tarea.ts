export type EstadoTarea = 'Pendiente' | 'En Progreso' | 'En Revisión' | 'Bloqueada' | 'Completada';
export type PrioridadTarea = 'Baja' | 'Media' | 'Alta' | 'Urgente';
export type UrgenciaTarea = 'Urgente' | 'No Urgente';

export interface Tarea {
  id: string;
  titulo: string;
  descripcion?: string;
  proyecto_id?: string;
  workflow_id?: string;
  estado: EstadoTarea;
  prioridad: PrioridadTarea;
  urgencia: UrgenciaTarea;
  fecha_vencimiento?: string;
  fecha_completado?: string;
  asignado_a?: string;
  creado_por?: string;
  tarea_padre_id?: string;
  info_adicional?: string;
  created_at: string;
  updated_at: string;
  
  // Related data
  asignado_usuario?: {
    id: string;
    nombre: string;
    email: string;
  };
  creador_usuario?: {
    id: string;
    nombre: string;
    email: string;
  };
  proyecto_nombre?: string;
  tarea_padre?: string;
  subtareas?: Tarea[];
  empresas_asociadas?: Array<{
    id: string;
    nombre: string;
  }>;
  etiquetas?: Array<{
    id: string;
    nombre: string;
    color: string;
  }>;
  
  // Calculated fields
  dias_vencimiento?: number;
  es_vencida: boolean;
}

export interface TareaCreate {
  titulo: string;
  descripcion?: string;
  proyecto_id?: string;
  workflow_id?: string;
  estado?: EstadoTarea;
  prioridad?: PrioridadTarea;
  urgencia?: UrgenciaTarea;
  fecha_vencimiento?: string;
  fecha_completado?: string;
  asignado_a?: string;
  tarea_padre_id?: string;
  info_adicional?: string;
  empresas_asociadas_ids?: string[];
  etiquetas_ids?: string[];
}

export interface TareaUpdate {
  titulo?: string;
  descripcion?: string;
  proyecto_id?: string;
  workflow_id?: string;
  estado?: EstadoTarea;
  prioridad?: PrioridadTarea;
  urgencia?: UrgenciaTarea;
  fecha_vencimiento?: string;
  asignado_a?: string;
  tarea_padre_id?: string;
  info_adicional?: string;
}

export interface TareaSummary {
  id: string;
  titulo: string;
  estado: EstadoTarea;
  prioridad: PrioridadTarea;
  urgencia: UrgenciaTarea;
  fecha_vencimiento?: string;
  asignado_a?: string;
  asignado_nombre?: string;
  proyecto_id?: string;
  proyecto_nombre?: string;
  dias_vencimiento?: number;
  es_vencida: boolean;
}

export interface TareaMatrix {
  urgente_importante: TareaSummary[];
  no_urgente_importante: TareaSummary[];
  urgente_no_importante: TareaSummary[];
  no_urgente_no_importante: TareaSummary[];
}

export interface TareaKanbanColumn {
  estado: EstadoTarea;
  tareas: TareaSummary[];
  total: number;
}

export interface TareaKanbanBoard {
  columnas: TareaKanbanColumn[];
  total_tareas: number;
}

export interface TareaListResponse {
  tareas: Tarea[];
  total: number;
  page: number;
  size: number;
}

export interface TareaFilters {
  estado?: EstadoTarea;
  prioridad?: PrioridadTarea;
  urgencia?: UrgenciaTarea;
  proyecto_id?: string;
  asignado_a?: string;
  empresa_id?: string;
  vencidas?: boolean;
  search?: string;
}

// Constants
export const ESTADOS_TAREA: EstadoTarea[] = [
  'Pendiente',
  'En Progreso',
  'En Revisión',
  'Bloqueada',
  'Completada'
];

export const PRIORIDADES_TAREA: PrioridadTarea[] = [
  'Baja',
  'Media',
  'Alta',
  'Urgente'
];

export const URGENCIAS_TAREA: UrgenciaTarea[] = [
  'Urgente',
  'No Urgente'
];

// Color mappings for UI
export const ESTADO_TAREA_COLORS: Record<EstadoTarea, string> = {
  'Pendiente': 'bg-gray-100 text-gray-800',
  'En Progreso': 'bg-blue-100 text-blue-800',
  'En Revisión': 'bg-purple-100 text-purple-800',
  'Bloqueada': 'bg-red-100 text-red-800',
  'Completada': 'bg-green-100 text-green-800'
};

export const PRIORIDAD_TAREA_COLORS: Record<PrioridadTarea, string> = {
  'Baja': 'bg-gray-100 text-gray-800',
  'Media': 'bg-blue-100 text-blue-800',
  'Alta': 'bg-orange-100 text-orange-800',
  'Urgente': 'bg-red-100 text-red-800'
};

export const URGENCIA_COLORS: Record<UrgenciaTarea, string> = {
  'Urgente': 'bg-red-100 text-red-800',
  'No Urgente': 'bg-green-100 text-green-800'
};

// Matrix quadrant labels
export const MATRIX_QUADRANTS = {
  urgente_importante: {
    title: 'Urgente e Importante',
    subtitle: 'Hacer ahora',
    color: 'bg-red-50 border-red-200',
    headerColor: 'bg-red-100 text-red-800'
  },
  no_urgente_importante: {
    title: 'No Urgente pero Importante',
    subtitle: 'Planificar',
    color: 'bg-orange-50 border-orange-200',
    headerColor: 'bg-orange-100 text-orange-800'
  },
  urgente_no_importante: {
    title: 'Urgente pero No Importante',
    subtitle: 'Delegar',
    color: 'bg-yellow-50 border-yellow-200',
    headerColor: 'bg-yellow-100 text-yellow-800'
  },
  no_urgente_no_importante: {
    title: 'No Urgente ni Importante',
    subtitle: 'Eliminar',
    color: 'bg-gray-50 border-gray-200',
    headerColor: 'bg-gray-100 text-gray-800'
  }
} as const;
