import { useState, useEffect, useCallback } from 'react';
import { 
  Proyecto, 
  ProyectoCreate, 
  ProyectoUpdate, 
  ProyectoListResponse, 
  ProyectoSummary,
  ProyectoFilters 
} from '../types/proyecto';
import { apiClient, api } from '../lib/api';

export const useProjects = () => {
  const [projects, setProjects] = useState<Proyecto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  const fetchProjects = useCallback(async (
    page: number = 1,
    filters?: ProyectoFilters
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        skip: ((page - 1) * pageSize).toString(),
        limit: pageSize.toString(),
      });

      if (filters?.estado) params.append('estado', filters.estado);
      if (filters?.prioridad) params.append('prioridad', filters.prioridad);
      if (filters?.responsable_id) params.append('responsable_id', filters.responsable_id);
      if (filters?.search) params.append('search', filters.search);

      const response = await apiClient.proyectos.getAll(Object.fromEntries(params)) as ProyectoListResponse;
      
      setProjects(response.proyectos);
      setTotal(response.total);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching projects');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  const createProject = useCallback(async (projectData: ProyectoCreate): Promise<Proyecto> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.proyectos.create(projectData) as Proyecto;
      
      // Refresh the list
      await fetchProjects(currentPage);
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error creating project';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [fetchProjects, currentPage]);

  const updateProject = useCallback(async (
    projectId: string, 
    updateData: ProyectoUpdate
  ): Promise<Proyecto> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.proyectos.update(projectId, updateData) as Proyecto;
      
      // Update the project in the local state
      setProjects(prev => prev.map(p => p.id === projectId ? response : p));
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating project';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProject = useCallback(async (projectId: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await apiClient.proyectos.delete(projectId);
      
      // Remove the project from local state
      setProjects(prev => prev.filter(p => p.id !== projectId));
      setTotal(prev => prev - 1);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting project';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getProject = useCallback(async (projectId: string): Promise<Proyecto> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.proyectos.getById(projectId) as Proyecto;
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error fetching project';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize with first page
  useEffect(() => {
    fetchProjects(1);
  }, [fetchProjects]);

  return {
    projects,
    loading,
    error,
    total,
    currentPage,
    pageSize,
    fetchProjects,
    createProject,
    updateProject,
    deleteProject,
    getProject,
    setError
  };
};

export const useProjectSummary = () => {
  const [projectsSummary, setProjectsSummary] = useState<ProyectoSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProjectsSummary = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.get<ProyectoSummary[]>('/api/v1/proyectos/dashboard');
      setProjectsSummary(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching projects summary');
      console.error('Error fetching projects summary:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProjectsSummary();
  }, [fetchProjectsSummary]);

  return {
    projectsSummary,
    loading,
    error,
    fetchProjectsSummary,
    setError
  };
};

export const useProjectStates = () => {
  const [estados, setEstados] = useState<string[]>([]);
  const [prioridades, setPrioridades] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchStates = useCallback(async () => {
    setLoading(true);
    
    try {
      const [estadosResponse, prioridadesResponse] = await Promise.all([
        apiClient.proyectos.getEstados(),
        apiClient.proyectos.getPrioridades()
      ]) as [{ estados: string[] }, { prioridades: string[] }];
      
      setEstados(estadosResponse.estados);
      setPrioridades(prioridadesResponse.prioridades);
    } catch (err) {
      console.error('Error fetching project states:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStates();
  }, [fetchStates]);

  return {
    estados,
    prioridades,
    loading,
    fetchStates
  };
};
