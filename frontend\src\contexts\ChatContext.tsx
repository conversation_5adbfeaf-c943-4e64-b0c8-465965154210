import { createContext, Dispatch, SetStateAction } from 'react';

// Define the structure of a chat message for the frontend
// Based on backend/app/models/chat.py::ChatMessage
export interface ChatMessage {
  id?: string; // Primary key from the 'threads' table (UUID as string)
  thread_id: number | string | bigint; // Allow multiple types due to DB int8
  content: string | null;
  type: string | null; // e.g., "user", "agent", "answer", "observation", "tool_use", "tool_output"
  from_sender: 'User' | 'Agent' | null; // Alias for 'from'
  message_id: number | null; // Using number for Decimal/numeric type from backend
  agent_id: string | null; // UUID as string
  user_id: string | null; // UUID as string
  created_at: string; // ISO string date
  intermediate_steps?: ChatMessage[]; // Optional array for nested steps
  tool_name?: string | null; // Optional tool name fetched from backend
  // For "Send to Docs" feature
  doc_export_status?: 'pending' | 'success' | 'error' | null;
  doc_export_url?: string | null;
}
 
// Define the shape of the context data (export it)
export interface ChatContextProps {
  currentThreadId: number | null;
  setCurrentThreadId: Dispatch<SetStateAction<number | null>>;
  messages: ChatMessage[];
  selectedAgentId: string | null; // UUID as string
  setSelectedAgentId: (agentId: string | null) => void; // Expect specific function signature
  isProcessingMessage: boolean; // Indicates if waiting for agent response
  isContextPanelOpen: boolean;
  isLoadingHistory: boolean; // Indicates if loading historical messages
  isAgentSelectionRequired: boolean; // Indicates if user must select an agent
  // Functions
  addMessage: (message: ChatMessage) => void;
  clearMessages: () => void;
  startProcessing: () => void;
  stopProcessing: () => void;
  toggleContextPanel: () => void;
  startNewConversation: () => void;
  clearCache: () => void; // Added cache management function
  // Doc Export Functions
  subscribeToDocExport: (messageId: string) => void;
  unsubscribeFromDocExport: (messageId: string) => void;
}

// Create the context with a default value (export it)
export const ChatContext = createContext<ChatContextProps | undefined>(undefined);

// ChatProvider component is now in src/providers/ChatProvider.tsx
// useChat hook is now in src/hooks/useChat.ts