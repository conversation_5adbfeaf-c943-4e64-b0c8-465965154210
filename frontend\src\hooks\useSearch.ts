import { useState, useCallback } from 'react';
import { 
  GlobalSearchResponse, 
  QuickSearchResponse, 
  SearchResult,
  SearchFiltersResponse
} from '../types/search';
import { apiClient } from '../lib/api';

export const useSearch = () => {
  const [results, setResults] = useState<GlobalSearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (
    query: string,
    options?: {
      limit?: number;
      includeProyectos?: boolean;
      includeProcesos?: boolean;
      includeTareas?: boolean;
    }
  ) => {
    if (!query.trim()) {
      setResults(null);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        q: query.trim(),
      });

      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.includeProyectos !== undefined) params.append('include_proyectos', options.includeProyectos.toString());
      if (options?.includeProcesos !== undefined) params.append('include_procesos', options.includeProcesos.toString());
      if (options?.includeTareas !== undefined) params.append('include_tareas', options.includeTareas.toString());

      const response = await apiClient.search.global(query.trim(), Object.fromEntries(params)) as GlobalSearchResponse;
      setResults(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error performing search');
      console.error('Error performing search:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults(null);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    search,
    clearResults,
    setError
  };
};

export const useQuickSearch = () => {
  const [results, setResults] = useState<QuickSearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const quickSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      setResults(null);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // const params = new URLSearchParams({
      //   q: query.trim(),
      // });

      const response = await apiClient.search.quick(query.trim()) as QuickSearchResponse;
      setResults(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error performing quick search');
      console.error('Error performing quick search:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults(null);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    quickSearch,
    clearResults,
    setError
  };
};

export const useSearchByType = () => {
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchByType = useCallback(async (
    query: string,
    _searchType: 'proyectos' | 'procesos' | 'tareas',
    _limit: number = 20
  ) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // const params = new URLSearchParams({
      //   q: query.trim(),
      //   limit: limit.toString(),
      // });

      const response = await apiClient.search.global(query.trim()) as SearchResult[];
      setResults(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error performing search by type');
      console.error('Error performing search by type:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    searchByType,
    clearResults,
    setError
  };
};

export const useSearchSuggestions = () => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getSuggestions = useCallback(async (query: string, limit: number = 10) => {
    if (!query.trim()) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // const params = new URLSearchParams({
      //   q: query.trim(),
      //   limit: limit.toString(),
      // });

      const response = await apiClient.search.suggestions(query.trim(), limit) as string[];
      setSuggestions(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error getting search suggestions');
      console.error('Error getting search suggestions:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(null);
  }, []);

  return {
    suggestions,
    loading,
    error,
    getSuggestions,
    clearSuggestions,
    setError
  };
};

export const useSearchFilters = () => {
  const [filters, setFilters] = useState<SearchFiltersResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFilters = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.search.global('') as SearchFiltersResponse;
      setFilters(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching search filters');
      console.error('Error fetching search filters:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    filters,
    loading,
    error,
    fetchFilters,
    setError
  };
};
