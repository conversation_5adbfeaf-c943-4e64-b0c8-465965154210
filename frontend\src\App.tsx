import React from 'react'; // Explicit import to potentially resolve JSX namespace issue
import { Routes, Route, Navigate } from 'react-router-dom';
import LoginPage from './pages/LoginPage';
import ChatPage from './pages/ChatPage';
import HistoryPage from './pages/HistoryPage';
import MeetingsListPage from './pages/MeetingsListPage'; // Added
import MeetingProcessingForm from './components/Meetings/MeetingProcessingForm'; // Added for /new route
import MeetingDetailsView from './pages/MeetingDetailsView'; // Added
import FileUploadTest from './components/Debug/FileUploadTest'; // Added for testing
// import RealtimeDiagnostics from './components/Diagnostics/RealtimeDiagnostics'; // Added for diagnostics - Commented out as file is missing

// Project Management Pages
import DashboardPage from './components/Dashboard/DashboardPage';
import ProjectsPage from './pages/ProjectsPage';
import ProcessesPage from './pages/ProcessesPage';
import TasksPage from './pages/TasksPage';
import TaskDetailsPage from './pages/TaskDetailsPage';
import TaskCreateForm from './components/Tasks/TaskCreateForm';
import TaskEditForm from './components/Tasks/TaskEditForm';

// Import other necessary components or contexts if needed
import { useAuth } from './hooks/useAuth'; // Corrected import path
import { ChatProvider } from './providers/ChatProvider'; // Corrected import path
import AppShell from './components/AppShell/AppShell'; // Import AppShell

// Removed placeholder isAuthenticated function

// Updated ProtectedRoute component using AuthContext
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    // Optionally return a loading spinner or null while checking auth state
    // Returning null prevents rendering children or redirecting prematurely
    return <div>Loading...</div>; // Or return null;
  }

  if (!isAuthenticated) {
    // Redirect them to the /login page
    return <Navigate to="/login" replace />;
  }

  // If authenticated and not loading, render the children
  return children;
};

// Component to handle root path redirection based on auth state
const RootRedirect = () => {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
        return <div>Loading...</div>; // Or return null;
    }

    return isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />;
};


function App() {
  return (
    <Routes>
      {/* Public route */}
      <Route path="/login" element={<LoginPage />} />

      {/* Protected routes */}
      <Route
        path="/chat"
        element={
          <ProtectedRoute>
            <AppShell>
              <ChatProvider> {/* Wrap ChatPage with ChatProvider */}
                <ChatPage />
              </ChatProvider>
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/history"
        element={
          <ProtectedRoute>
            <AppShell>
              <ChatProvider> {/* Wrap HistoryPage with ChatProvider */}
                <HistoryPage />
              </ChatProvider>
            </AppShell>
          </ProtectedRoute>
        }
      />
      {/* Meeting Routes */}
      <Route
        path="/meetings"
        element={
          <ProtectedRoute>
            <AppShell>
              <MeetingsListPage />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/meetings/new"
        element={
          <ProtectedRoute>
            <AppShell>
              <MeetingProcessingForm />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/meetings/:reunionId"
        element={
          <ProtectedRoute>
            <AppShell>
              <MeetingDetailsView />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/meetings/process/:reunionId" // New route for continuing processing
        element={
          <ProtectedRoute>
            <AppShell>
              <MeetingProcessingForm />
            </AppShell>
          </ProtectedRoute>
        }
      />

      {/* Project Management Routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <AppShell>
              <DashboardPage />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/proyectos"
        element={
          <ProtectedRoute>
            <AppShell>
              <ProjectsPage />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/procesos"
        element={
          <ProtectedRoute>
            <AppShell>
              <ProcessesPage />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/tareas"
        element={
          <ProtectedRoute>
            <AppShell>
              <TasksPage />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/tareas/nueva"
        element={
          <ProtectedRoute>
            <AppShell>
              <TaskCreateForm />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/tareas/:id"
        element={
          <ProtectedRoute>
            <AppShell>
              <TaskDetailsPage />
            </AppShell>
          </ProtectedRoute>
        }
      />
      <Route
        path="/tareas/:id/editar"
        element={
          <ProtectedRoute>
            <AppShell>
              <TaskEditForm />
            </AppShell>
          </ProtectedRoute>
        }
      />

      {/* Debug Routes - Temporary for development */}
      <Route
        path="/debug/file-upload"
        element={
          <ProtectedRoute>
            <AppShell>
              <FileUploadTest />
            </AppShell>
          </ProtectedRoute>
        }
      />

      {/* Diagnostic Routes - Temporary for development */}
      {/*
      <Route
        path="/diagnostics/realtime"
        element={
          <ProtectedRoute>
            <AppShell>
              <RealtimeDiagnostics />
            </AppShell>
          </ProtectedRoute>
        }
      />
      */}

      {/* Redirect root path using the new component */}
      <Route
        path="/"
        element={<RootRedirect />}
      />

      {/* Optional: Catch-all route for 404 Not Found */}
      <Route path="*" element={<div>404 Not Found</div>} />
    </Routes>
  );
}

export default App;

