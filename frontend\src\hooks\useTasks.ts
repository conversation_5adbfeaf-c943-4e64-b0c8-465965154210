import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Tarea, 
  TareaCreate, 
  TareaUpdate, 
  TareaListResponse, 
  TareaMatrix,
  TareaKanbanBoard,
  TareaFilters,
  EstadoTarea,
  PrioridadTarea,
  UrgenciaTarea
} from '../types/tarea';
import { apiClient, api } from '../lib/api';

interface ProyectoOption {
  id: string;
  nombre: string;
}

interface EmpresaOption {
  id: string;
  nombre: string;
}

export const useTasks = () => {
  const [tasks, setTasks] = useState<Tarea[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  const fetchTasks = useCallback(async (
    page: number = 1,
    filters?: TareaFilters
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        skip: ((page - 1) * pageSize).toString(),
        limit: pageSize.toString(),
      });

      if (filters?.estado) params.append('estado', filters.estado);
      if (filters?.prioridad) params.append('prioridad', filters.prioridad);
      if (filters?.urgencia) params.append('urgencia', filters.urgencia);
      if (filters?.proyecto_id) params.append('proyecto_id', filters.proyecto_id);
      if (filters?.asignado_a) params.append('asignado_a', filters.asignado_a);
      if (filters?.empresa_id) params.append('empresa_id', filters.empresa_id);
      if (filters?.vencidas !== undefined) params.append('vencidas', filters.vencidas.toString());
      if (filters?.search) params.append('search', filters.search);

      const response = await apiClient.tareas.getAll(Object.fromEntries(params)) as TareaListResponse;
      
      setTasks(response.tareas);
      setTotal(response.total);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching tasks');
      console.error('Error fetching tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  const createTask = useCallback(async (taskData: TareaCreate): Promise<Tarea> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.tareas.create(taskData) as Tarea;
      
      // Refresh the list
      await fetchTasks(currentPage);
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error creating task';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [fetchTasks, currentPage]);

  const updateTask = useCallback(async (
    taskId: string, 
    updateData: TareaUpdate
  ): Promise<Tarea> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.tareas.update(taskId, updateData) as Tarea;
      
      // Update the task in the local state
      setTasks(prev => prev.map(t => t.id === taskId ? response : t));
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating task';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteTask = useCallback(async (taskId: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await apiClient.tareas.delete(taskId);
      
      // Remove the task from local state
      setTasks(prev => prev.filter(t => t.id !== taskId));
      setTotal(prev => prev - 1);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting task';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getTask = useCallback(async (taskId: string): Promise<Tarea> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.tareas.getById(taskId) as Tarea;
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error fetching task';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkUpdateStatus = useCallback(async (
    taskIds: string[], 
    newStatus: EstadoTarea
  ): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await api.patch('/api/v1/tareas/bulk/estado', {
        tarea_ids: taskIds,
        nuevo_estado: newStatus
      });
      
      // Update local state
      setTasks(prev => prev.map(task => 
        taskIds.includes(task.id) ? { ...task, estado: newStatus } : task
      ));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating tasks status';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize with first page
  useEffect(() => {
    fetchTasks(1);
  }, [fetchTasks]);

  return {
    tasks,
    loading,
    error,
    total,
    currentPage,
    pageSize,
    fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    getTask,
    bulkUpdateStatus,
    setError
  };
};

export const useTaskMatrix = () => {
  const [matrix, setMatrix] = useState<TareaMatrix | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMatrix = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.tareas.getMatrix() as TareaMatrix;
      setMatrix(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching task matrix');
      console.error('Error fetching task matrix:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchMatrix();
  }, [fetchMatrix]);

  return {
    matrix,
    loading,
    error,
    fetchMatrix,
    setError
  };
};

export const useTaskKanban = (filters?: TareaFilters) => {
  const [kanbanBoard, setKanbanBoard] = useState<TareaKanbanBoard | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize the filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [
    filters?.proyecto_id,
    filters?.asignado_a,
    filters?.empresa_id,
    filters?.estado,
    filters?.prioridad,
    filters?.urgencia,
    filters?.vencidas,
    filters?.search
  ]);

  const fetchKanban = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (memoizedFilters?.proyecto_id) params.append('proyecto_id', memoizedFilters.proyecto_id);
      if (memoizedFilters?.asignado_a) params.append('asignado_a', memoizedFilters.asignado_a);
      if (memoizedFilters?.empresa_id) params.append('empresa_id', memoizedFilters.empresa_id);
      if (memoizedFilters?.estado) params.append('estado', memoizedFilters.estado);
      if (memoizedFilters?.prioridad) params.append('prioridad', memoizedFilters.prioridad);
      if (memoizedFilters?.urgencia) params.append('urgencia', memoizedFilters.urgencia);
      if (memoizedFilters?.vencidas !== undefined) params.append('vencidas', memoizedFilters.vencidas.toString());
      if (memoizedFilters?.search) params.append('search', memoizedFilters.search);

      const response = await apiClient.tareas.getKanban(Object.fromEntries(params)) as TareaKanbanBoard;
      setKanbanBoard(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error fetching kanban board');
      console.error('Error fetching kanban board:', err);
    } finally {
      setLoading(false);
    }
  }, [memoizedFilters]);

  useEffect(() => {
    fetchKanban();
  }, [fetchKanban]);

  return {
    kanbanBoard,
    loading,
    error,
    fetchKanban,
    setError
  };
};

export const useProyectos = () => {
  const [proyectos, setProyectos] = useState<ProyectoOption[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchProyectos = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.tareas.getProyectos();
      setProyectos(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Error fetching proyectos:', error);
      setProyectos([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProyectos();
  }, [fetchProyectos]);

  return {
    proyectos,
    loading,
    fetchProyectos
  };
};

export const useEmpresas = () => {
  const [empresas, setEmpresas] = useState<EmpresaOption[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchEmpresas = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.tareas.getEmpresas();
      setEmpresas(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Error fetching empresas:', error);
      setEmpresas([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEmpresas();
  }, [fetchEmpresas]);

  return {
    empresas,
    loading,
    fetchEmpresas
  };
};

export const useTaskStates = () => {
  const [estados, setEstados] = useState<EstadoTarea[]>([]);
  const [prioridades, setPrioridades] = useState<PrioridadTarea[]>([]);
  const [urgencias, setUrgencias] = useState<UrgenciaTarea[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchStates = useCallback(async () => {
    setLoading(true);
    
    try {
      const [estadosResponse, prioridadesResponse, urgenciasResponse] = await Promise.all([
        apiClient.tareas.getEstados(),
        apiClient.tareas.getPrioridades(),
        apiClient.tareas.getUrgencias()
      ]) as [{ estados: EstadoTarea[] }, { prioridades: PrioridadTarea[] }, { urgencias: UrgenciaTarea[] }];
      
      setEstados(estadosResponse.estados);
      setPrioridades(prioridadesResponse.prioridades);
      setUrgencias(urgenciasResponse.urgencias);
    } catch (err) {
      console.error('Error fetching task states:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStates();
  }, [fetchStates]);

  return {
    estados,
    prioridades,
    urgencias,
    loading,
    fetchStates
  };
};
