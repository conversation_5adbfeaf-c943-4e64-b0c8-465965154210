import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react-swc'
import tailwindcss from '@tailwindcss/vite' // Import the new Vite plugin

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')

  console.log(`🔧 Vite Config - Mode: ${mode}, Command: ${command}`)
  console.log(`🔧 VITE_API_URL: ${env.VITE_API_URL}`)
  console.log(`🔧 VITE_ENVIRONMENT: ${env.VITE_ENVIRONMENT}`)

  return {
    plugins: [
      react(),
      tailwindcss(), // Use the Tailwind Vite plugin
    ],
    // Define global constants that can be replaced at build time
    define: {
      __APP_ENV__: JSON.stringify(env.VITE_ENVIRONMENT),
      __API_URL__: JSON.stringify(env.VITE_API_URL),
    },
    // Server configuration for development
    server: {
      port: 5173,
      host: true, // Allow external connections
    },
    // Build configuration
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      // Ensure environment variables are available during build
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            supabase: ['@supabase/supabase-js'],
          },
        },
      },
    },
  }
})
